"""
瓶组管理数据模型
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON
from sqlalchemy.orm import relationship

from config.database import Base


class BottleMaintenance(Base):
    """
    瓶组管理表
    """
    __tablename__ = 'bottle_maintenance'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    bottle_code = Column(String(20), nullable=False, unique=True, comment='瓶组编码，BOL+6位递增数')
    bottle_type = Column(String(100), nullable=False, comment='容器类型，如玻璃瓶、木盒')
    bottle_volume = Column(String(50), nullable=False, comment='容器容量，如100ML,1kg,>1L,500mL')
    storage_styles = Column(JSON, nullable=True, comment='存储方式，JSON数组，如["冷藏","避光"]')
    fix_styles = Column(JSON, nullable=True, comment='固定方式，JSON数组，如["尽快安装","吊顶上"]')
    sample_age = Column(Integer, nullable=True, comment='样品时效数值')
    sample_age_unit = Column(String(20), nullable=True, comment='样品时效单位，如小时、天')
    remark = Column(Text, nullable=True, comment='备注')
    
    # 通用字段
    create_by = Column(String(50), nullable=True, comment='创建人')
    create_time = Column(DateTime, nullable=True, comment='创建时间')
    update_by = Column(String(50), nullable=True, comment='更新人')
    update_time = Column(DateTime, nullable=True, comment='更新时间')

    # 关联技术手册（多对多关系）
    # 注意：这里暂时注释掉，需要在技术手册模块中添加对应的back_populates
    # technical_manuals = relationship(
    #     "TechnicalManual",
    #     secondary="bottle_maintenance_technical_manual",
    #     back_populates="bottle_maintenances"
    # )

    # 关联检测方法（一对多关系）
    method_relations = relationship("BottleMaintenanceMethod", back_populates="bottle_maintenance", cascade="all, delete-orphan")

"""
瓶组管理与检测方法关联表数据模型
"""

from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, UniqueConstraint, Index, Text
from sqlalchemy.orm import relationship
from config.database import Base


class BottleMaintenanceMethod(Base):
    """
    瓶组管理与检测方法关联表
    """

    __tablename__ = "bottle_maintenance_method"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    bottle_maintenance_id = Column(Integer, ForeignKey("bottle_maintenance.id"), nullable=False, comment="瓶组管理ID")
    detection_method = Column(Text, nullable=False, comment="检测方法")

    # 通用字段
    create_by = Column(String(50), nullable=True, comment="创建人")
    create_time = Column(DateTime, nullable=True, comment="创建时间")
    update_by = Column(String(50), nullable=True, comment="更新人")
    update_time = Column(DateTime, nullable=True, comment="更新时间")

    # 关联瓶组管理（多对一关系）
    bottle_maintenance = relationship("BottleMaintenance", back_populates="method_relations")

    # 索引和约束
    __table_args__ = (
        UniqueConstraint('bottle_maintenance_id', 'detection_method', name='uk_bottle_method'),
        Index('idx_bottle_maintenance_id', 'bottle_maintenance_id'),
        Index('idx_detection_method', 'detection_method'),
        {'comment': '瓶组管理与检测方法关联表'}
    )

    def __repr__(self):
        return f"<BottleMaintenanceMethod(id={self.id}, bottle_maintenance_id={self.bottle_maintenance_id}, detection_method='{self.detection_method}')>"

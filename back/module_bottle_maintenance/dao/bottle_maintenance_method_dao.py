"""
瓶组与检测方法关联数据访问对象
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, and_

from module_bottle_maintenance.entity.do.bottle_maintenance_method_do import BottleMaintenanceMethod
from module_bottle_maintenance.entity.do.bottle_maintenance_do import BottleMaintenance


class BottleMaintenanceMethodDAO:
    """瓶组与检测方法关联数据访问对象"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_bottle_method(self, bottle_method: BottleMaintenanceMethod) -> BottleMaintenanceMethod:
        """创建瓶组与检测方法关联记录"""
        self.db.add(bottle_method)
        await self.db.flush()
        await self.db.refresh(bottle_method)
        return bottle_method
    
    async def batch_create_bottle_methods(self, bottle_methods: List[BottleMaintenanceMethod]) -> List[BottleMaintenanceMethod]:
        """批量创建瓶组与检测方法关联记录"""
        self.db.add_all(bottle_methods)
        await self.db.flush()
        for method in bottle_methods:
            await self.db.refresh(method)
        return bottle_methods
    
    async def get_bottle_method_by_id(self, method_id: int) -> Optional[BottleMaintenanceMethod]:
        """根据ID获取瓶组与检测方法关联记录"""
        stmt = select(BottleMaintenanceMethod).where(BottleMaintenanceMethod.id == method_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_bottle_methods_by_bottle_id(self, bottle_maintenance_id: int) -> List[BottleMaintenanceMethod]:
        """根据瓶组ID获取关联的检测方法列表"""
        stmt = select(BottleMaintenanceMethod).where(BottleMaintenanceMethod.bottle_maintenance_id == bottle_maintenance_id)
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def get_bottle_methods_by_detection_method(self, detection_method: str) -> List[BottleMaintenanceMethod]:
        """根据检测方法获取关联的瓶组列表"""
        stmt = select(BottleMaintenanceMethod).where(BottleMaintenanceMethod.detection_method == detection_method)
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def get_bottle_by_detection_method(self, detection_method: str) -> Optional[BottleMaintenance]:
        """根据检测方法获取匹配的瓶组"""
        stmt = select(BottleMaintenance).join(
            BottleMaintenanceMethod,
            BottleMaintenance.id == BottleMaintenanceMethod.bottle_maintenance_id
        ).where(BottleMaintenanceMethod.detection_method == detection_method)
        
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_bottle_method_by_bottle_and_method(self, bottle_maintenance_id: int, detection_method: str) -> Optional[BottleMaintenanceMethod]:
        """根据瓶组ID和检测方法获取关联记录"""
        stmt = select(BottleMaintenanceMethod).where(
            and_(
                BottleMaintenanceMethod.bottle_maintenance_id == bottle_maintenance_id,
                BottleMaintenanceMethod.detection_method == detection_method
            )
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def delete_bottle_method(self, method_id: int) -> bool:
        """删除瓶组与检测方法关联记录"""
        stmt = delete(BottleMaintenanceMethod).where(BottleMaintenanceMethod.id == method_id)
        result = await self.db.execute(stmt)
        return result.rowcount > 0
    
    async def delete_bottle_methods_by_bottle_id(self, bottle_maintenance_id: int) -> bool:
        """删除瓶组下的所有检测方法关联记录"""
        stmt = delete(BottleMaintenanceMethod).where(BottleMaintenanceMethod.bottle_maintenance_id == bottle_maintenance_id)
        result = await self.db.execute(stmt)
        return result.rowcount > 0
    
    async def delete_bottle_method_by_bottle_and_method(self, bottle_maintenance_id: int, detection_method: str) -> bool:
        """删除指定瓶组和检测方法的关联记录"""
        stmt = delete(BottleMaintenanceMethod).where(
            and_(
                BottleMaintenanceMethod.bottle_maintenance_id == bottle_maintenance_id,
                BottleMaintenanceMethod.detection_method == detection_method
            )
        )
        result = await self.db.execute(stmt)
        return result.rowcount > 0

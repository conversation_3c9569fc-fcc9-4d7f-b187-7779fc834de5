"""
样品记录DTO模型
"""

from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel


class SampleRecordCreateDTO(BaseModel):
    """样品记录创建DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)

    # 关联字段
    sampling_task_group_id: int = Field(..., description="采样任务分组ID")
    sample_number: int = Field(..., description="样品序号")
    sample_type: Optional[str] = Field(None, description="样品类型")
    sample_source: Optional[str] = Field(None, description="样品来源")
    point_name: Optional[str] = Field(None, description="点位名称")
    cycle_number: int = Field(..., description="周期序号")
    cycle_type: Optional[str] = Field(None, description="周期类型")
    detection_category: Optional[str] = Field(None, description="检测类别")
    detection_parameter: Optional[str] = Field(None, description="检测参数")
    detection_method: Optional[str] = Field(None, description="检测方法")
    remark: Optional[str] = Field(None, description="备注")


class SampleRecordUpdateDTO(BaseModel):
    """样品记录更新DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    id: int = Field(..., description="样品记录ID")
    status: Optional[int] = Field(None, description="样品状态")
    collection_time: Optional[datetime] = Field(None, description="采集时间")
    submission_time: Optional[datetime] = Field(None, description="送检时间")
    completion_time: Optional[datetime] = Field(None, description="完成时间")
    remark: Optional[str] = Field(None, description="备注")


class SampleRecordDTO(BaseModel):
    """样品记录DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)

    id: int = Field(..., description="样品记录ID")
    sampling_task_group_id: int = Field(..., description="采样任务分组ID")
    sample_number: int = Field(..., description="样品序号")
    sample_type: Optional[str] = Field(None, description="样品类型")
    sample_source: Optional[str] = Field(None, description="样品来源")
    point_name: Optional[str] = Field(None, description="点位名称")
    cycle_number: int = Field(..., description="周期序号")
    cycle_type: Optional[str] = Field(None, description="周期类型")
    detection_category: Optional[str] = Field(None, description="检测类别")
    detection_parameter: Optional[str] = Field(None, description="检测参数")
    detection_method: Optional[str] = Field(None, description="检测方法")
    status: int = Field(..., description="样品状态")
    status_label: Optional[str] = Field(None, description="状态标签")
    collection_time: Optional[datetime] = Field(None, description="采集时间")
    submission_time: Optional[datetime] = Field(None, description="送检时间")
    completion_time: Optional[datetime] = Field(None, description="完成时间")
    remark: Optional[str] = Field(None, description="备注")
    create_by: Optional[int] = Field(None, description="创建人")
    create_time: Optional[datetime] = Field(None, description="创建时间")
    update_by: Optional[int] = Field(None, description="更新人")
    update_time: Optional[datetime] = Field(None, description="更新时间")
    
    # 关联信息
    task_code: Optional[str] = Field(None, description="任务编号")
    task_name: Optional[str] = Field(None, description="任务名称")
    project_name: Optional[str] = Field(None, description="项目名称")
    customer_name: Optional[str] = Field(None, description="客户名称")
    creator_name: Optional[str] = Field(None, description="创建人姓名")
    updater_name: Optional[str] = Field(None, description="更新人姓名")


class SampleRecordQueryDTO(BaseModel):
    """样品记录查询DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    page_num: int = Field(1, description="页码")
    page_size: int = Field(10, description="每页大小")
    sampling_task_assignment_id: Optional[int] = Field(None, description="采样任务执行指派ID")
    detection_cycle_item_id: Optional[int] = Field(None, description="检测周期条目ID")
    status: Optional[int] = Field(None, description="样品状态")
    sample_type: Optional[str] = Field(None, description="样品类型")
    point_name: Optional[str] = Field(None, description="点位名称")
    detection_category: Optional[str] = Field(None, description="检测类别")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")


class SampleRecordBatchCreateDTO(BaseModel):
    """样品记录批量创建DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    sampling_task_assignment_id: int = Field(..., description="采样任务执行指派ID")


class SampleRecordBatchUpdateStatusDTO(BaseModel):
    """样品记录批量更新状态DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    record_ids: List[int] = Field(..., description="样品记录ID列表")
    status: int = Field(..., description="目标状态")


class SampleRecordStatisticsDTO(BaseModel):
    """样品记录统计DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    total_count: int = Field(0, description="总数量")
    pending_count: int = Field(0, description="待采集数量")
    collected_count: int = Field(0, description="已采集数量")
    submitted_count: int = Field(0, description="已送检数量")
    testing_count: int = Field(0, description="检测中数量")
    completed_count: int = Field(0, description="已完成数量")

"""
瓶组功能集成测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from module_sampling.service.sampling_bottle_group_service import SamplingBottleGroupService
from module_sampling.entity.do.sampling_bottle_group_do import SamplingBottleGroup
from module_bottle_maintenance.entity.do.bottle_maintenance_method_do import BottleMaintenanceMethod
from module_bottle_maintenance.entity.do.bottle_maintenance_do import BottleMaintenance


class TestBottleGroupIntegration:
    """瓶组功能集成测试"""
    
    @pytest.fixture
    async def db_session(self):
        """获取数据库会话"""
        async with get_db_session() as session:
            yield session
    
    async def test_bottle_group_service_initialization(self, db_session: AsyncSession):
        """测试瓶组服务初始化"""
        service = SamplingBottleGroupService(db_session)
        assert service is not None
        assert service.db == db_session
        assert service.bottle_group_dao is not None
        assert service.bottle_group_sample_dao is not None
        assert service.bottle_method_dao is not None
    
    async def test_bottle_maintenance_method_creation(self, db_session: AsyncSession):
        """测试瓶组与检测方法关联创建"""
        # 首先需要有瓶组管理记录
        bottle_maintenance = BottleMaintenance(
            bottle_code="BOL000001",
            bottle_type="玻璃瓶",
            bottle_volume="100ML",
            create_by="test_user"
        )
        db_session.add(bottle_maintenance)
        await db_session.flush()
        await db_session.refresh(bottle_maintenance)
        
        # 创建瓶组与检测方法关联
        bottle_method = BottleMaintenanceMethod(
            bottle_maintenance_id=bottle_maintenance.id,
            detection_method="GB/T 5750.4-2006",
            create_by="test_user"
        )
        db_session.add(bottle_method)
        await db_session.flush()
        await db_session.refresh(bottle_method)
        
        assert bottle_method.id is not None
        assert bottle_method.bottle_maintenance_id == bottle_maintenance.id
        assert bottle_method.detection_method == "GB/T 5750.4-2006"
        
        # 清理测试数据
        await db_session.delete(bottle_method)
        await db_session.delete(bottle_maintenance)
        await db_session.commit()
    
    async def test_bottle_group_creation(self, db_session: AsyncSession):
        """测试瓶组记录创建"""
        bottle_group = SamplingBottleGroup(
            sampling_task_id=1,
            bottle_group_code="ST2408001-B001",
            bottle_maintenance_id=0,  # 默认瓶组
            detection_method="GB/T 5750.4-2006",
            sample_count=3,
            create_by=1
        )
        
        db_session.add(bottle_group)
        await db_session.flush()
        await db_session.refresh(bottle_group)
        
        assert bottle_group.id is not None
        assert bottle_group.bottle_group_code == "ST2408001-B001"
        assert bottle_group.bottle_maintenance_id == 0
        assert bottle_group.sample_count == 3
        
        # 清理测试数据
        await db_session.delete(bottle_group)
        await db_session.commit()
    
    async def test_bottle_group_query(self, db_session: AsyncSession):
        """测试瓶组查询功能"""
        service = SamplingBottleGroupService(db_session)
        
        # 创建测试数据
        bottle_group = SamplingBottleGroup(
            sampling_task_id=999,
            bottle_group_code="TEST-B001",
            bottle_maintenance_id=0,
            detection_method="测试方法",
            sample_count=1,
            create_by=1
        )
        
        db_session.add(bottle_group)
        await db_session.flush()
        await db_session.refresh(bottle_group)
        
        # 测试按任务ID查询
        bottle_groups = await service.bottle_group_dao.get_bottle_groups_by_task_id(999)
        assert len(bottle_groups) == 1
        assert bottle_groups[0].bottle_group_code == "TEST-B001"
        
        # 测试按ID查询
        found_group = await service.bottle_group_dao.get_bottle_group_by_id(bottle_group.id)
        assert found_group is not None
        assert found_group.bottle_group_code == "TEST-B001"
        
        # 清理测试数据
        await db_session.delete(bottle_group)
        await db_session.commit()


def run_integration_tests():
    """运行集成测试"""
    print("开始瓶组功能集成测试...")
    
    async def run_tests():
        test_instance = TestBottleGroupIntegration()
        
        try:
            async with get_db_session() as session:
                print("✓ 数据库连接成功")
                
                # 测试服务初始化
                await test_instance.test_bottle_group_service_initialization(session)
                print("✓ 瓶组服务初始化测试通过")
                
                # 测试瓶组记录创建
                await test_instance.test_bottle_group_creation(session)
                print("✓ 瓶组记录创建测试通过")
                
                # 测试瓶组查询
                await test_instance.test_bottle_group_query(session)
                print("✓ 瓶组查询测试通过")
                
                print("✅ 所有集成测试通过！")
                
        except Exception as e:
            print(f"❌ 集成测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    asyncio.run(run_tests())


if __name__ == "__main__":
    run_integration_tests()

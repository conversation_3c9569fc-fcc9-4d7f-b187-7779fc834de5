"""
瓶组功能简单测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_imports():
    """测试所有模块导入"""
    print("开始测试模块导入...")
    
    try:
        from module_sampling.entity.do.sampling_bottle_group_do import SamplingBottleGroup
        print("✓ SamplingBottleGroup 导入成功")
    except ImportError as e:
        print(f"✗ SamplingBottleGroup 导入失败: {e}")
        return False
    
    try:
        from module_sampling.entity.do.sampling_bottle_group_sample_do import SamplingBottleGroupSample
        print("✓ SamplingBottleGroupSample 导入成功")
    except ImportError as e:
        print(f"✗ SamplingBottleGroupSample 导入失败: {e}")
        return False
    
    try:
        from module_sampling.entity.do.sampling_bottle_group_sequence_do import SamplingBottleGroupSequence
        print("✓ SamplingBottleGroupSequence 导入成功")
    except ImportError as e:
        print(f"✗ SamplingBottleGroupSequence 导入失败: {e}")
        return False
    
    try:
        from module_bottle_maintenance.entity.do.bottle_maintenance_method_do import BottleMaintenanceMethod
        print("✓ BottleMaintenanceMethod 导入成功")
    except ImportError as e:
        print(f"✗ BottleMaintenanceMethod 导入失败: {e}")
        return False
    
    try:
        from module_sampling.dao.sampling_bottle_group_dao import SamplingBottleGroupDAO
        print("✓ SamplingBottleGroupDAO 导入成功")
    except ImportError as e:
        print(f"✗ SamplingBottleGroupDAO 导入失败: {e}")
        return False
    
    try:
        from module_sampling.service.sampling_bottle_group_service import SamplingBottleGroupService
        print("✓ SamplingBottleGroupService 导入成功")
    except ImportError as e:
        print(f"✗ SamplingBottleGroupService 导入失败: {e}")
        return False
    
    try:
        from module_sampling.controller.sampling_bottle_group_controller import router
        print("✓ 瓶组控制器 导入成功")
    except ImportError as e:
        print(f"✗ 瓶组控制器 导入失败: {e}")
        return False
    
    try:
        from module_sampling.entity.vo.sampling_bottle_group_vo import (
            SamplingBottleGroupModel,
            SamplingBottleGroupDetailModel,
            BottleGroupGenerateResponseModel
        )
        print("✓ 瓶组VO模型 导入成功")
    except ImportError as e:
        print(f"✗ 瓶组VO模型 导入失败: {e}")
        return False
    
    return True


def test_model_creation():
    """测试模型创建"""
    print("\n开始测试模型创建...")
    
    try:
        from module_sampling.entity.do.sampling_bottle_group_do import SamplingBottleGroup
        
        # 创建瓶组实例
        bottle_group = SamplingBottleGroup(
            sampling_task_id=1,
            bottle_group_code="TEST-B001",
            bottle_maintenance_id=0,
            detection_method="测试方法",
            sample_count=3,
            create_by=1
        )
        
        assert bottle_group.sampling_task_id == 1
        assert bottle_group.bottle_group_code == "TEST-B001"
        assert bottle_group.bottle_maintenance_id == 0
        assert bottle_group.detection_method == "测试方法"
        assert bottle_group.sample_count == 3
        assert bottle_group.create_by == 1
        
        print("✓ SamplingBottleGroup 模型创建成功")
        
    except Exception as e:
        print(f"✗ SamplingBottleGroup 模型创建失败: {e}")
        return False
    
    try:
        from module_bottle_maintenance.entity.do.bottle_maintenance_method_do import BottleMaintenanceMethod
        
        # 创建瓶组方法关联实例
        bottle_method = BottleMaintenanceMethod(
            bottle_maintenance_id=1,
            detection_method="GB/T 5750.4-2006",
            create_by="test_user"
        )
        
        assert bottle_method.bottle_maintenance_id == 1
        assert bottle_method.detection_method == "GB/T 5750.4-2006"
        assert bottle_method.create_by == "test_user"
        
        print("✓ BottleMaintenanceMethod 模型创建成功")
        
    except Exception as e:
        print(f"✗ BottleMaintenanceMethod 模型创建失败: {e}")
        return False
    
    return True


def test_vo_models():
    """测试VO模型"""
    print("\n开始测试VO模型...")
    
    try:
        from module_sampling.entity.vo.sampling_bottle_group_vo import (
            SamplingBottleGroupModel,
            SamplingBottleGroupDetailModel,
            BottleGroupGenerateResponseModel
        )
        
        # 测试基础模型
        bottle_group_model = SamplingBottleGroupModel(
            sampling_task_id=1,
            bottle_group_code="TEST-B001",
            bottle_maintenance_id=0,
            detection_method="测试方法",
            sample_count=3
        )
        
        assert bottle_group_model.sampling_task_id == 1
        assert bottle_group_model.bottle_group_code == "TEST-B001"
        
        print("✓ SamplingBottleGroupModel 创建成功")
        
        # 测试详情模型
        bottle_group_detail = SamplingBottleGroupDetailModel(
            sampling_task_id=1,
            bottle_group_code="TEST-B001",
            bottle_maintenance_id=0,
            detection_method="测试方法",
            sample_count=3,
            bottle_type="玻璃瓶",
            bottle_volume="100ML"
        )
        
        assert bottle_group_detail.bottle_type == "玻璃瓶"
        assert bottle_group_detail.bottle_volume == "100ML"
        
        print("✓ SamplingBottleGroupDetailModel 创建成功")
        
        # 测试响应模型
        response_model = BottleGroupGenerateResponseModel(
            total_groups=5,
            default_groups=2,
            matched_groups=3,
            bottle_groups=[]
        )
        
        assert response_model.total_groups == 5
        assert response_model.default_groups == 2
        assert response_model.matched_groups == 3
        
        print("✓ BottleGroupGenerateResponseModel 创建成功")
        
    except Exception as e:
        print(f"✗ VO模型测试失败: {e}")
        return False
    
    return True


def test_server_integration():
    """测试服务器集成"""
    print("\n开始测试服务器集成...")
    
    try:
        from server import app
        print("✓ 服务器应用导入成功")
        
        # 检查路由是否注册
        routes = [route.path for route in app.routes]
        bottle_routes = [route for route in routes if '/sampling/bottle-groups' in route]
        
        if bottle_routes:
            print(f"✓ 瓶组路由已注册: {bottle_routes}")
        else:
            print("✗ 瓶组路由未找到")
            return False
        
    except Exception as e:
        print(f"✗ 服务器集成测试失败: {e}")
        return False
    
    return True


def main():
    """主测试函数"""
    print("=" * 50)
    print("瓶组功能集成测试")
    print("=" * 50)
    
    all_passed = True
    
    # 测试导入
    if not test_imports():
        all_passed = False
    
    # 测试模型创建
    if not test_model_creation():
        all_passed = False
    
    # 测试VO模型
    if not test_vo_models():
        all_passed = False
    
    # 测试服务器集成
    if not test_server_integration():
        all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✅ 所有测试通过！瓶组功能已成功集成")
    else:
        print("❌ 部分测试失败，请检查错误信息")
    print("=" * 50)
    
    return all_passed


if __name__ == "__main__":
    main()

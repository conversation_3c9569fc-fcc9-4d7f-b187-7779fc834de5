<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="任务编号" prop="taskCode">
        <el-input
          v-model="queryParams.taskCode"
          placeholder="请输入任务编号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入任务名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择任务状态" clearable style="width: 200px">
          <el-option
            v-for="dictItem in (dict.type && dict.type.sampling_task_status) || []"
            :key="dictItem.value"
            :label="dictItem.label"
            :value="dictItem.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="负责人" prop="responsibleUserId">
        <el-select v-model="queryParams.responsibleUserId" placeholder="请选择负责人" clearable style="width: 200px">
          <el-option
            v-for="user in userList || []"
            :key="user.userId"
            :label="user.nickName"
            :value="user.userId"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="任务编号" align="center" prop="taskCode" width="180">
          <template #default="scope">
            <span v-if="scope?.row?.isUrgent" class="urgent-prefix">【加急】</span>{{ scope?.row?.taskCode }}
          </template>
        </el-table-column>
        <el-table-column label="任务名称" align="center" prop="taskName" :show-overflow-tooltip="true" />
        <el-table-column label="项目报价" align="center" prop="projectName" :show-overflow-tooltip="true" />
        <el-table-column label="任务状态" align="center" prop="statusLabel" width="100">
          <template #default="scope">
            <span>{{ scope?.row?.statusLabel }}</span>
          </template>
        </el-table-column>

        <el-table-column label="负责人" align="center" prop="responsibleUserName" width="100" />
        <el-table-column label="计划开始时间" align="center" prop="plannedStartDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope?.row?.plannedStartDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="计划结束时间" align="center" prop="plannedEndDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope?.row?.plannedEndDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope?.row?.createTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300">
          <template #default="scope">
            <el-button
              type="text"
              icon="Edit"
              @click="handleUpdate(scope?.row)"
              v-hasPermi="['sampling:task:edit']"
            >修改</el-button>
            <el-button
              type="text"
              icon="View"
              @click="handleDetail(scope?.row)"
              v-hasPermi="['sampling:task:query']"
            >详情</el-button>
            <el-button
              type="text"
              icon="User"
              @click="handleAssignExecutorFromList(scope?.row)"
              v-hasPermi="['sampling:task:assign']"
            >指派执行人</el-button>
            <el-button
              type="text"
              :icon="scope?.row?.isUrgent ? 'RemoveFilled' : 'Flag'"
              @click="handleUrgent(scope?.row)"
              v-if="hasUrgentPermission"
              :style="{ color: scope?.row?.isUrgent ? 'var(--el-color-danger)' : 'var(--el-color-primary)' }"
            >{{ scope?.row?.isUrgent ? '取消加急' : '加急' }}</el-button>
            <el-button
              type="text"
              icon="Delete"
              @click="handleDelete(scope?.row)"
              v-hasPermi="['sampling:task:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改采样任务对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="任务名称" prop="taskName">
              <el-input v-model="form.taskName" placeholder="请输入任务名称" :disabled="form.id != null" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目报价" prop="projectQuotationId">
              <el-select v-model="form.projectQuotationId" placeholder="请选择项目报价" @change="handleProjectQuotationChange" v-if="!form.id">
                <el-option
                  v-for="quotation in quotationList || []"
                  :key="quotation.id"
                  :label="quotation.projectName"
                  :value="quotation.id"
                />
              </el-select>
              <el-input v-else :value="getProjectQuotationName(form.projectQuotationId)" :disabled="form.id != null"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="负责人" prop="responsibleUserId">
              <el-select v-model="form.responsibleUserId" placeholder="请选择负责人">
                <el-option
                  v-for="user in userList || []"
                  :key="user.userId"
                  :label="user.nickName"
                  :value="user.userId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务状态" prop="status">
              <el-input :value="getStatusLabel(form.status)" :disabled="form.id != null"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="计划开始时间" prop="plannedStartDate">
              <el-date-picker
                v-model="form.plannedStartDate"
                type="date"
                placeholder="选择计划开始时间"
                value-format="YYYY-MM-DD"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划结束时间" prop="plannedEndDate">
              <el-date-picker
                v-model="form.plannedEndDate"
                type="date"
                placeholder="选择计划结束时间"
                value-format="YYYY-MM-DD"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="任务组员" prop="memberUserIds">
          <el-select
            v-model="form.memberUserIds"
            multiple
            placeholder="请选择任务组员"
            style="width: 100%"
          >
            <el-option
              v-for="user in userList || []"
              :key="user.userId"
              :label="user.nickName"
              :value="user.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="任务描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入任务描述" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 任务详情对话框 -->
    <el-dialog title="任务详情" v-model="detailOpen" width="1400px" append-to-body class="detail-dialog">
      <div class="detail-content">
        <el-descriptions :column="2" border class="detail-descriptions">
          <el-descriptions-item label="任务编号">
              <span v-if="taskDetail.isUrgent" class="urgent-prefix">【加急】</span>{{ taskDetail.taskCode }}
            </el-descriptions-item>
          <el-descriptions-item label="任务名称">{{ taskDetail.taskName }}</el-descriptions-item>
          <el-descriptions-item label="项目报价">{{ taskDetail.projectName }}</el-descriptions-item>
          <el-descriptions-item label="任务状态">
            <span>{{ taskDetail.statusLabel }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="负责人">{{ taskDetail.responsibleUserName }}</el-descriptions-item>
          <el-descriptions-item label="任务组员">
            <span v-if="taskDetail.memberUsers && taskDetail.memberUsers.length > 0">
              {{ taskDetail.memberUsers.map(user => user.nickName).join(', ') }}
            </span>
            <span v-else class="text-gray-400">暂无组员</span>
          </el-descriptions-item>
          <el-descriptions-item label="创建用户">{{ taskDetail.createUserName }}</el-descriptions-item>
          <el-descriptions-item label="计划开始时间">{{ parseTime(taskDetail.plannedStartDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <el-descriptions-item label="计划结束时间">{{ parseTime(taskDetail.plannedEndDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <el-descriptions-item label="实际开始时间">{{ parseTime(taskDetail.actualStartDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <el-descriptions-item label="实际结束时间">{{ parseTime(taskDetail.actualEndDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <el-descriptions-item label="任务描述" :span="2">{{ taskDetail.description || '暂无描述' }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ taskDetail.remark || '暂无备注' }}</el-descriptions-item>
        </el-descriptions>
        
        <!-- 关联的检测周期条目 -->
        <div class="cycle-items-section">
          <div class="section-header">
            <h4 class="section-title">关联的检测周期条目</h4>
            <div class="section-actions">
              <el-button size="small" type="text" @click="expandAllGroups">
                <i class="el-icon-arrow-down"></i> 展开所有
              </el-button>
              <el-button size="small" type="text" @click="collapseAllGroups">
                <i class="el-icon-arrow-up"></i> 折叠所有
              </el-button>
              <template v-if="detailMode === 'assign'">
                <el-divider direction="vertical"></el-divider>
                <el-button
                  size="small"
                  type="primary"
                  :disabled="!isBatchAssignEnabled"
                  @click="handleBatchAssignExecutor"
                >
                  <i class="el-icon-user"></i> 批量指派执行人 ({{ selectedCycleItems.length }})
                </el-button>
              </template>
            </div>
          </div>
          <el-table ref="cycleItemsTable" :data="groupedCycleItems" border class="cycle-items-table" :span-method="arraySpanMethod" style="width: 100%" @selection-change="handleCycleItemSelectionChange">
            <el-table-column v-if="detailMode === 'assign'" type="selection" width="55" align="center" :selectable="isRowSelectable" />
            <el-table-column label="周期序号" prop="cycleNumber" align="center" width="80" />
            <el-table-column label="周期类型" prop="cycleType" align="center" width="80" :show-overflow-tooltip="true" />
            <el-table-column label="检测类别" prop="detectionCategory" align="center" width="100" :show-overflow-tooltip="true" />
            <el-table-column label="点位名称" prop="pointName" align="center" width="120" :show-overflow-tooltip="true" />
            <el-table-column label="检测参数" prop="detectionParameter" align="center" min-width="150" :show-overflow-tooltip="true">
              <template #default="scope">
                <div class="detection-parameter-cell">
                  <span>{{ scope.row.detectionParameter }}</span>
                  <el-button
                  v-if="scope.row.isFirstInGroup && scope.row.hasMultipleItems"
                  size="small"
                  type="text"
                  class="collapse-btn"
                  @click="toggleGroupCollapse(scope.row.groupKey)"
                >
                    <i :class="scope.row.isCollapsed ? 'el-icon-arrow-right' : 'el-icon-arrow-down'"></i>
                    <span class="collapse-text">{{ scope.row.isCollapsed ? `展开(${scope.row.groupSize})` : '折叠' }}</span>
                  </el-button>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="检测方法" prop="detectionMethod" align="center" min-width="150" :show-overflow-tooltip="true" />
            <el-table-column label="样品来源" prop="sampleSource" align="center" width="100" :show-overflow-tooltip="true" />
            <el-table-column label="状态" prop="statusLabel" align="center" width="80">
              <template #default="scope">
                <span>{{ scope.row?.statusLabel }}</span>
              </template>
            </el-table-column>
            <el-table-column label="执行人" align="center" width="120">
              <template #default="scope">
                <span v-if="scope.row.isFirstInGroup && scope.row.assignedExecutors">
                  {{ scope.row.assignedExecutors.join(', ') }}
                </span>
                <span v-else-if="scope.row.isFirstInGroup" class="text-gray-400">未指派</span>
              </template>
            </el-table-column>
            <el-table-column v-if="detailMode === 'assign'" label="操作" align="center" width="120">
              <template #default="scope">
                <el-button
                  v-if="scope.row.isFirstInGroup"
                  link
                  type="primary"
                  size="small"
                  @click="handleAssignExecutor(scope.row)"
                >
                  {{ scope.row.assignedExecutors && scope.row.assignedExecutors.length > 0 ? '重新指派执行人' : '指派执行人' }}
                </el-button>
              </template>
            </el-table-column>

          </el-table>
        </div>
      </div>
    </el-dialog>

    <!-- 执行人指派对话框 -->
    <el-dialog title="执行人指派" v-model="assignmentOpen" width="600px" append-to-body>
      <el-form ref="assignmentForm" :model="assignmentForm" label-width="120px">
        <el-form-item label="分组信息">
          <div class="assignment-group-info">
            <p><strong>周期序号：</strong>{{ assignmentForm.cycleNumber }}</p>
            <p><strong>周期类型：</strong>{{ assignmentForm.cycleType }}</p>
            <p><strong>检测类别：</strong>{{ assignmentForm.detectionCategory }}</p>
            <p><strong>点位名称：</strong>{{ assignmentForm.pointName }}</p>
          </div>
        </el-form-item>
        <el-form-item label="执行人" prop="assignedUserIds" :rules="[{ required: true, message: '请选择执行人', trigger: 'change' }]">
          <el-select
            v-model="assignmentForm.assignedUserIds"
            multiple
            placeholder="请选择执行人"
            style="width: 100%"
          >
            <el-option
              v-for="user in taskMemberList"
              :key="user.userId"
              :label="user.nickName"
              :value="user.userId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="assignmentOpen = false">取 消</el-button>
          <el-button type="primary" @click="submitAssignment">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量执行人指派对话框 -->
    <el-dialog title="批量指派执行人" v-model="batchAssignmentOpen" width="800px" append-to-body>
      <el-form ref="batchAssignmentForm" :model="batchAssignmentForm" label-width="120px">
        <el-form-item label="选中条目">
          <div class="batch-assignment-items">
            <el-table :data="batchAssignmentForm.selectedItems" border size="small" max-height="300">
              <el-table-column label="周期序号" prop="cycleNumber" align="center" width="80" />
              <el-table-column label="周期类型" prop="cycleType" align="center" width="80" />
              <el-table-column label="检测类别" prop="detectionCategory" align="center" width="100" />
              <el-table-column label="点位名称" prop="pointName" align="center" width="120" />
              <el-table-column label="检测参数" prop="detectionParameter" align="center" min-width="120" />
              <el-table-column label="检测方法" prop="detectionMethod" align="center" min-width="120" />
            </el-table>
            <div class="batch-summary">
              <span class="summary-text">共选中 <strong>{{ batchAssignmentForm.selectedItems.length }}</strong> 个分组</span>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="执行人" prop="assignedUserIds" :rules="[{ required: true, message: '请选择执行人', trigger: 'change' }]">
          <el-select
            v-model="batchAssignmentForm.assignedUserIds"
            multiple
            placeholder="请选择执行人"
            style="width: 100%"
          >
            <el-option
              v-for="user in userList"
              :key="user.userId"
              :label="user.nickName || user.userName"
              :value="user.userId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchAssignmentOpen = false">取 消</el-button>
          <el-button type="primary" @click="submitBatchAssignment">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { listSamplingTask, getSamplingTask, delSamplingTask, addSamplingTask, updateSamplingTask, setSamplingTaskUrgent, getTaskMembers } from "@/api/sampling/samplingTask";

import { listUser } from "@/api/system/user";
import { listProjectQuotation } from "@/api/quotation/projectQuotation";
import { getDicts } from "@/api/system/dict/data";
import { assignExecutorsToGroups } from "@/api/sampling/taskGroup";
import useUserStore from '@/store/modules/user';

export default {
  name: "SamplingTask",
  dicts: ['sampling_task_status', 'detection_cycle_status'],
  computed: {
    // 检查用户是否有加急权限（task-urgent权限或admin角色）
    hasUrgentPermission() {
      const userStore = useUserStore();
      console.log('用户权限:', userStore.permissions);
      console.log('用户角色:', userStore.roles);
      const hasTaskUrgentPermission = userStore.permissions.some(permission =>
        permission === '*:*:*' || permission === 'task-urgent'
      );
      const isAdmin = userStore.roles.includes('admin');
      console.log('是否有task-urgent权限:', hasTaskUrgentPermission);
      console.log('是否为admin角色:', isAdmin);
      const result = hasTaskUrgentPermission || isAdmin;
      console.log('最终权限结果:', result);
      return result;
    },
    // 批量指派按钮是否可用
    isBatchAssignEnabled() {
      return this.selectedCycleItems && this.selectedCycleItems.length > 0;
    }
  },
  data() {
    return {
      // 字典数据
      dict: {
        type: {
          sampling_task_status: [],
          detection_cycle_status: []
        }
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采样任务表格数据
      taskList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 详情弹窗模式：'view' 查看详情，'assign' 指派执行人
      detailMode: 'view',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskCode: null,
        taskName: null,
        status: null,
        responsibleUserId: null
      },
      // 表单参数
      form: {},
      // 任务详情
      taskDetail: {},
      // 聚合后的检测周期条目
      groupedCycleItems: [],
      // 单元格合并信息
      spanArr: [],
      pos: 0,
      // 分组折叠状态管理
      groupCollapseState: new Map(), // 存储每个分组的折叠状态
      // 批量选择状态管理
      selectedCycleItems: [], // 选中的检测周期条目
      selectAll: false, // 全选状态
      batchAssignmentOpen: false, // 批量指派对话框
      batchAssignmentForm: {
        taskId: null,
        selectedItems: [],
        assignedUserIds: []
      },
      // 执行人指派对话框
      assignmentOpen: false,
      assignmentForm: {
        taskId: null,
        cycleNumber: null,
        cycleType: null,
        detectionCategory: null,
        pointName: null,
        assignedUserIds: []
      },
      // 表单校验
      rules: {
        taskName: [
          { required: true, message: "任务名称不能为空", trigger: "blur" }
        ],
        projectQuotationId: [
          { required: true, message: "项目报价不能为空", trigger: "change" }
        ],
        responsibleUserId: [
          { required: true, message: "负责人不能为空", trigger: "change" }
        ],
        status: [
          { required: true, message: "任务状态不能为空", trigger: "change" }
        ]
      },
      // 用户列表
      userList: [],
      // 任务组员列表
      taskMemberList: [],
      // 项目报价列表
      quotationList: []
    };
  },
  created() {
    this.getDictData();
    this.getList();
    this.getUserList();
    this.getQuotationList();
  },
  methods: {
    /** 获取字典数据 */
    getDictData() {
      // 获取采样任务状态字典
      getDicts('sampling_task_status').then(response => {
        this.dict.type.sampling_task_status = response.data.map(p => ({
          label: p.dictLabel,
          value: p.dictValue,
          elTagType: p.listClass,
          elTagClass: p.cssClass
        }));
      }).catch(error => {
        console.error('获取采样任务状态字典失败:', error);
        this.dict.type.sampling_task_status = [];
      });
      
      // 获取检测周期状态字典
      getDicts('detection_cycle_status').then(response => {
        this.dict.type.detection_cycle_status = response.data.map(p => ({
          label: p.dictLabel,
          value: p.dictValue,
          elTagType: p.listClass,
          elTagClass: p.cssClass
        }));
      }).catch(error => {
        console.error('获取检测周期状态字典失败:', error);
        this.dict.type.detection_cycle_status = [];
      });
    },
    /** 查询采样任务列表 */
    getList() {
      this.loading = true;
      listSamplingTask(this.queryParams).then(response => {
        // 处理接口返回的数据结构：response.data.records
        const records = response.data?.records || [];
        this.taskList = records.filter(item => item != null);
        this.total = response.data?.total || 0;
        this.loading = false;
      }).catch(error => {
        console.error('获取采样任务列表失败:', error);
        this.taskList = [];
        this.total = 0;
        this.loading = false;
      });
    },
    /** 查询用户列表 */
    getUserList() {
      listUser({ pageSize: 100000 }).then(response => {
        this.userList = response.rows || [];
      }).catch(error => {
        console.error('获取用户列表失败:', error);
        this.userList = [];
      });
    },
    /** 加载任务组员列表 */
    loadTaskMembers(taskId) {
      getTaskMembers(taskId).then(response => {
        this.taskMemberList = response.data || [];
      }).catch(error => {
        console.error('加载任务组员失败:', error);
        this.taskMemberList = [];
        this.$modal.msgError("加载任务组员失败，将显示所有用户");
        // 如果加载组员失败，回退到显示所有用户
        this.taskMemberList = this.userList;
      });
    },
    /** 查询项目报价列表 */
    getQuotationList() {
      listProjectQuotation({ status: 'approved' }).then(response => {
        this.quotationList = response.rows || [];
      }).catch(error => {
        console.error('获取项目报价列表失败:', error);
        this.quotationList = [];
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        taskName: null,
        projectQuotationId: null,
        responsibleUserId: null,
        memberUserIds: [],
        status: "pending",
        plannedStartDate: null,
        plannedEndDate: null,
        description: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加采样任务";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const taskId = row.id || this.ids
      getSamplingTask(taskId).then(response => {
        this.form = response.data;
        // 处理组员信息
        if (response.data.memberUsers && Array.isArray(response.data.memberUsers)) {
          this.form.memberUserIds = response.data.memberUsers.map(user => user.userId);
        } else {
          this.form.memberUserIds = [];
        }
        this.open = true;
        this.title = "修改采样任务";
      });
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      // 清空选择状态
      this.selectedCycleItems = [];
      const taskId = row.id;
      getSamplingTask(taskId).then(response => {
        this.taskDetail = response.data;
        this.processGroupedCycleItems();
        this.detailMode = 'view'; // 设置为查看模式
        this.detailOpen = true;
        // 确保表格选择状态被清空
        this.$nextTick(() => {
          if (this.$refs.cycleItemsTable) {
            this.$refs.cycleItemsTable.clearSelection();
          }
        });
      });
    },

    /** 重新加载任务详情（保持当前模式） */
    reloadTaskDetail() {
      if (this.taskDetail && this.taskDetail.id) {
        const taskId = this.taskDetail.id;
        getSamplingTask(taskId).then(response => {
          this.taskDetail = response.data;
          this.processGroupedCycleItems();
          // 不改变 detailMode，保持当前模式
          // 确保表格选择状态被清空
          this.$nextTick(() => {
            if (this.$refs.cycleItemsTable) {
              this.$refs.cycleItemsTable.clearSelection();
            }
          });
        });
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 从select组件直接获取当前值进行最终同步
          if (this.$refs.responsibleUserSelect) {
            const selectValue = this.$refs.responsibleUserSelect.value;
            if (selectValue !== undefined && selectValue !== this.form.responsibleUserId) {
              console.log('检测到数据不同步，强制同步:', selectValue, '->', this.form.responsibleUserId);
              this.$set(this.form, 'responsibleUserId', selectValue);
            }
          }

          // 添加短暂延迟确保所有数据更新完成
          setTimeout(() => {
            console.log('提交前的表单数据:', JSON.stringify(this.form, null, 2));
            console.log('提交前负责人ID:', this.form.responsibleUserId);

            if (this.form.id != null) {
              updateSamplingTask(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }).catch(error => {
                console.error('更新失败:', error);
                this.$modal.msgError("修改失败，请重试");
              });
            } else {
              addSamplingTask(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }).catch(error => {
                console.error('新增失败:', error);
                this.$modal.msgError("新增失败，请重试");
              });
            }
          }, 100); // 100ms延迟确保数据更新
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const taskIds = row.id || this.ids;
      this.$modal.confirm('是否确认删除采样任务编号为"' + taskIds + '"的数据项？').then(function() {
        return delSamplingTask(taskIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('sampling/task/export', {
        ...this.queryParams
      }, `task_${new Date().getTime()}.xlsx`)
    },

    /** 加急按钮操作 */
    handleUrgent(row) {
      const isUrgent = !row.isUrgent;
      const action = isUrgent ? '设置' : '取消';
      this.$modal.confirm(`是否确认${action}任务"${row.taskName}"的加急状态？`).then(() => {
        setSamplingTaskUrgent(row.id, isUrgent).then(() => {
          this.$modal.msgSuccess(`${action}加急成功`);
          this.getList();
        }).catch(error => {
          console.error('设置加急状态失败:', error);
          this.$modal.msgError(`${action}加急失败`);
        });
      }).catch(() => {});
    },
    /** 处理聚合后的检测周期条目 */
    processGroupedCycleItems() {
      if (!this.taskDetail.cycleItems || this.taskDetail.cycleItems.length === 0) {
        this.groupedCycleItems = [];
        return;
      }

      // 按周期序号、周期类型、检测类别、点位名称聚合
      const groupMap = new Map();

      this.taskDetail.cycleItems.forEach(item => {
        // 确保字段映射正确，处理后端可能的字段名差异
        const processedItem = {
          ...item,
          detectionCategory: item.detectionCategory || item.category,
          detectionParameter: item.detectionParameter || item.parameter,
          detectionMethod: item.detectionMethod || item.method,
          pointName: item.pointName || item.point_name,
          cycleType: item.cycleType || item.cycle_type,
          sampleSource: item.sampleSource || item.sample_source || item.sampleSource
        };

        const groupKey = `${processedItem.cycleNumber}-${processedItem.cycleType || ''}-${processedItem.detectionCategory || ''}-${processedItem.pointName || ''}`;

        if (!groupMap.has(groupKey)) {
          groupMap.set(groupKey, {
            groupKey,
            cycleNumber: processedItem.cycleNumber,
            cycleType: processedItem.cycleType,
            detectionCategory: processedItem.detectionCategory,
            pointName: processedItem.pointName,
            items: []
          });

          // 初始化折叠状态，默认为折叠状态（当分组有多个条目时）
          if (!this.groupCollapseState.has(groupKey)) {
            this.groupCollapseState.set(groupKey, true); // true表示折叠，false表示展开
          }
        }

        groupMap.get(groupKey).items.push(processedItem);
      });

      // 转换为表格数据 - 每个条目都作为独立行，但标记分组信息
      this.groupedCycleItems = [];
      groupMap.forEach(group => {
        // 查找该分组的执行人指派信息（使用新的分组系统）
        let assignedExecutors = [];
        let groupId = null;
        if (this.taskDetail.taskGroups) {
          const taskGroup = this.taskDetail.taskGroups.find(g => {
            // 处理null值和空字符串的比较
            const gCycleType = g.cycleType || '';
            const gDetectionCategory = g.detectionCategory || '';
            const gPointName = g.pointName || '';
            const groupCycleType = group.cycleType || '';
            const groupDetectionCategory = group.detectionCategory || '';
            const groupPointName = group.pointName || '';

            return g.cycleNumber === group.cycleNumber &&
                   gCycleType === groupCycleType &&
                   gDetectionCategory === groupDetectionCategory &&
                   gPointName === groupPointName;
          });

          if (taskGroup) {
            groupId = taskGroup.id; // 保存分组ID
            // 优先使用后端返回的用户名列表
            if (taskGroup.assignedUserNames && taskGroup.assignedUserNames.length > 0) {
              assignedExecutors = taskGroup.assignedUserNames;
            } else if (taskGroup.assignedUserIds && taskGroup.assignedUserIds.length > 0) {
              // 如果没有用户名列表，则根据用户ID查找用户名
              assignedExecutors = taskGroup.assignedUserIds.map(userId => {
                const user = this.userList && this.userList.find(u => u.userId === userId);
                return user ? (user.nickName || user.userName) : `用户${userId}`;
              });
            }
          }
        }

        // 根据折叠状态决定显示的条目数量
        const isCollapsed = this.groupCollapseState.get(group.groupKey);
        const itemsToShow = isCollapsed && group.items.length > 1 ? [group.items[0]] : group.items;

        itemsToShow.forEach((item, index) => {
          this.groupedCycleItems.push({
            ...item,
            groupKey: group.groupKey,
            groupId: groupId, // 分组ID，用于执行人指派
            isFirstInGroup: index === 0, // 标记是否为组内第一个
            groupSize: group.items.length, // 组内条目数量（原始数量，不是显示数量）
            displaySize: itemsToShow.length, // 当前显示的条目数量
            assignedExecutors: assignedExecutors, // 执行人信息
            isCollapsed: isCollapsed, // 折叠状态
            hasMultipleItems: group.items.length > 1 // 是否有多个条目
          });
        });
      });

      // 计算合并信息
      this.calculateSpanArr();


    },

    /** 计算单元格合并信息 */
    calculateSpanArr() {
      this.spanArr = [];

      for (let i = 0; i < this.groupedCycleItems.length; i++) {
        const current = this.groupedCycleItems[i];

        if (current.isFirstInGroup) {
          // 如果是组内第一个，设置合并行数为当前显示的条目数量
          this.spanArr.push(current.displaySize);
        } else {
          // 如果不是组内第一个，设置为0（被合并）
          this.spanArr.push(0);
        }
      }
    },
    /** 表格单元格合并方法 */
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      // 需要合并的列：选择列(0)、周期序号(1)、周期类型(2)、检测类别(3)、点位名称(4)、执行人(9)、操作(10)
      if (columnIndex === 0 || columnIndex === 1 || columnIndex === 2 || columnIndex === 3 || columnIndex === 4 || columnIndex === 9 || columnIndex === 10) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      }
    },
    /** 从列表页面处理执行人指派 */
    handleAssignExecutorFromList(row) {
      const taskId = row.id;

      // 先检查任务组员
      getTaskMembers(taskId).then(membersResponse => {
        const members = membersResponse.data || [];

        if (members.length === 0) {
          this.$modal.msgWarning('该任务还没有选择任务组员，请先在任务详情中选择任务组员后再进行执行人指派');
          return;
        }

        // 有任务组员，继续执行原逻辑
        getSamplingTask(taskId).then(response => {
          this.taskDetail = response.data;
          this.processGroupedCycleItems();
          this.detailMode = 'assign'; // 设置为指派模式
          this.detailOpen = true;
        });
      }).catch(error => {
        console.error('获取任务组员失败:', error);
        this.$modal.msgError('获取任务组员信息失败，请稍后重试');
      });
    },
    /** 处理执行人指派 */
    handleAssignExecutor(row) {
      console.log('执行人指派:', row);
      this.assignmentForm = {
        taskId: this.taskDetail.id,
        cycleNumber: row.cycleNumber,
        cycleType: row.cycleType,
        detectionCategory: row.detectionCategory,
        pointName: row.pointName,
        assignedUserIds: []
      };

      // 加载任务组员列表
      this.loadTaskMembers(this.taskDetail.id);

      this.assignmentOpen = true;
    },
    /** 提交执行人指派 */
    submitAssignment() {
      this.$refs["assignmentForm"].validate(valid => {
        if (valid) {
          // 找到对应的分组ID
          const targetGroup = this.groupedCycleItems.find(item =>
            item.cycleNumber === this.assignmentForm.cycleNumber &&
            (item.cycleType || '') === (this.assignmentForm.cycleType || '') &&
            (item.detectionCategory || '') === (this.assignmentForm.detectionCategory || '') &&
            (item.pointName || '') === (this.assignmentForm.pointName || '')
          );

          if (!targetGroup || !targetGroup.groupId) {
            this.$modal.msgError("未找到对应的分组信息");
            return;
          }

          const requestData = {
            taskId: this.assignmentForm.taskId,
            groups: [{
              id: targetGroup.groupId,
              assignedUserIds: this.assignmentForm.assignedUserIds
            }]
          };

          assignExecutorsToGroups(requestData).then(response => {
            this.$modal.msgSuccess("执行人指派成功");
            this.assignmentOpen = false;
            // 重新加载任务详情
            this.reloadTaskDetail();
          }).catch(error => {
            console.error('执行人指派失败:', error);
            this.$modal.msgError("执行人指派失败");
          });
        }
      });
    },
    /** 获取项目报价名称 */
    getProjectQuotationName(quotationId) {
      // 优先使用form中的projectName字段（编辑模式下从详情接口获取）
      if (this.form.projectName) {
        return this.form.projectName;
      }
      // 如果没有，则从quotationList中查找（新增模式下使用）
      const quotation = this.quotationList.find(q => q.id === quotationId);
      return quotation ? quotation.projectName : '未知项目';
    },
    /** 获取状态标签 */
     getStatusLabel(status) {
       // 优先使用form中的statusLabel字段（编辑模式下从详情接口获取）
       if (this.form.statusLabel) {
         return this.form.statusLabel;
       }
       // 如果没有，则从字典中查找（新增模式下使用）
       const statusItem = this.dict.type.sampling_task_status.find(item => parseInt(item.value) === status);
       return statusItem ? statusItem.label : '未知状态';
     },
    /** 切换分组的折叠状态 */
    toggleGroupCollapse(groupKey) {
      const currentState = this.groupCollapseState.get(groupKey);
      this.groupCollapseState.set(groupKey, !currentState);

      // 重新处理数据以更新显示
      this.processGroupedCycleItems();
    },
    /** 展开所有分组 */
    expandAllGroups() {
      this.groupCollapseState.forEach((value, key) => {
        this.groupCollapseState.set(key, false);
      });
      this.processGroupedCycleItems();
    },
    /** 折叠所有分组 */
    collapseAllGroups() {
      this.groupCollapseState.forEach((value, key) => {
        this.groupCollapseState.set(key, true);
      });
      this.processGroupedCycleItems();
    },
    /** 判断行是否可选择 */
    isRowSelectable(row) {
      // 只有第一行可以选择，代表整个分组
      return row.isFirstInGroup;
    },
    /** 处理检测周期条目选择变化 */
    handleCycleItemSelectionChange(selection) {
      this.selectedCycleItems = selection;
    },
    /** 批量指派执行人 */
    handleBatchAssignExecutor() {
      if (this.selectedCycleItems.length === 0) {
        this.$modal.msgWarning("请先选择要指派的检测周期条目");
        return;
      }

      this.batchAssignmentForm = {
        taskId: this.taskDetail.id,
        selectedItems: this.selectedCycleItems.map(item => ({
          cycleNumber: item.cycleNumber,
          cycleType: item.cycleType,
          detectionCategory: item.detectionCategory,
          pointName: item.pointName,
          detectionParameter: item.detectionParameter,
          detectionMethod: item.detectionMethod
        })),
        assignedUserIds: []
      };

      // 加载任务组员列表
      this.loadTaskMembers(this.taskDetail.id);

      this.batchAssignmentOpen = true;
    },
    /** 提交批量执行人指派 */
    submitBatchAssignment() {
      this.$refs["batchAssignmentForm"].validate(valid => {
        if (valid) {
          // 需要找到对应的分组IDs
          // TODO: 这里需要根据选中的条目找到对应的分组IDs
          // 暂时使用模拟数据，实际应该从任务详情中获取分组信息
          const requestData = {
            taskId: this.batchAssignmentForm.taskId,
            groups: this.batchAssignmentForm.selectedItems.map((item, index) => ({
              id: index + 1, // TODO: 实际的分组ID
              assignedUserIds: this.batchAssignmentForm.assignedUserIds
            }))
          };

          assignExecutorsToGroups(requestData).then(response => {
            this.$modal.msgSuccess(`批量执行人指派成功，共处理 ${this.batchAssignmentForm.selectedItems.length} 个分组`);
            this.batchAssignmentOpen = false;
            // 清空选择
            this.selectedCycleItems = [];
            // 重新加载任务详情
            this.reloadTaskDetail();
          }).catch(error => {
            console.error('批量执行人指派失败:', error);
            this.$modal.msgError("批量执行人指派失败");
          });
        }
      });
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.card-subtitle {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
  margin-left: 15px;
}

.box-card {
  margin-top: 20px;
}

.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: var(--el-fill-color-light);
  color: var(--el-text-color-regular);
  font-weight: 600;
}

.el-button--link {
  color: var(--el-color-primary);
  font-weight: 500;
}

.el-button--link:hover {
  color: var(--el-color-primary-light-3);
  background-color: var(--el-color-primary-light-9);
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

.mb8 {
  margin-bottom: 8px;
}

/* 表单样式优化 */
.el-form-item {
  margin-bottom: 18px;
}

.el-input, .el-select {
  width: 100%;
}

/* 搜索表单样式 */
.el-form--inline .el-form-item {
  margin-right: 20px;
  margin-bottom: 15px;
}

/* 操作按钮区域 */
.el-row.mb8 {
  background-color: var(--el-bg-color-page);
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  border: 1px solid var(--el-border-color-light);
}

/* 表格操作列样式 */
.small-padding .cell {
  padding-left: 8px;
  padding-right: 8px;
}

.fixed-width .el-button {
  margin-right: 8px;
  margin-bottom: 4px;
}

/* 对话框样式优化 */
.el-dialog__header {
  background-color: var(--el-bg-color-page);
  border-bottom: 1px solid var(--el-border-color);
}

.el-dialog__title {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 详情页面样式 */
.el-descriptions {
  margin-bottom: 20px;
}

.el-descriptions__label {
  font-weight: 600;
  color: var(--el-text-color-regular);
}

/* Transfer 组件样式优化 */
.el-transfer {
  text-align: left;
}

.el-transfer-panel {
  border-radius: 6px;
}

.el-transfer-panel__header {
  background-color: var(--el-bg-color-page);
  border-bottom: 1px solid var(--el-border-color);
  font-weight: 600;
}

/* 状态标签样式 */
.dict-tag {
  font-weight: 500;
}

/* 搜索表单样式 */
.search-form {
  background-color: var(--el-bg-color-page);
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
  border: 1px solid var(--el-border-color-light);
}

/* 详情对话框样式 */
.detail-dialog .el-dialog__body {
  padding: 20px;
  height: 80vh;
  overflow: hidden;
}

.detail-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.detail-descriptions {
  margin-bottom: 24px;
  flex-shrink: 0;
}

.cycle-items-section {
  margin-top: 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.section-title {
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
  font-weight: 600;
  font-size: 16px;
  border-bottom: 2px solid var(--el-color-primary);
  padding-bottom: 8px;
  flex-shrink: 0;
}

.cycle-items-table {
  border-radius: 6px;
  overflow: hidden;
  flex: 1;
}

.cycle-items-table .el-table__body-wrapper {
  max-height: calc(80vh - 300px);
  overflow-y: auto;
}

/* 卡片头部样式增强 */
.box-card .el-card__header {
  background: linear-gradient(135deg, var(--el-bg-color-page) 0%, var(--el-fill-color-light) 100%);
  border-bottom: 2px solid var(--el-border-color);
}

/* 表格行悬停效果 */
.el-table tbody tr:hover > td {
  background-color: var(--el-fill-color-light) !important;
}

/* 按钮组样式优化 */
.el-row.mb8 .el-button {
  margin-right: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.el-row.mb8 .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 状态标签样式增强 */
.dict-tag {
  font-weight: 500;
  border-radius: 12px;
  padding: 4px 8px;
}

/* 加急前缀样式 */
.urgent-prefix {
  color: #d32f2f;
  font-weight: bold;
  margin-right: 4px;
}

/* 执行人指派对话框样式 */
.assignment-group-info {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.assignment-group-info p {
  margin: 4px 0;
  color: #606266;
}

/* 折叠功能样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-actions {
  display: flex;
  gap: 8px;
}

.section-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
  color: #409eff;
}

.section-actions .el-button:hover {
  color: #66b1ff;
  background-color: #ecf5ff;
}

.detection-parameter-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.collapse-btn {
  padding: 2px 4px !important;
  font-size: 11px !important;
  color: #409eff !important;
  border: 1px solid #d9ecff;
  border-radius: 3px;
  background-color: #ecf5ff;
  transition: all 0.3s ease;
}

.collapse-btn:hover {
  color: #66b1ff !important;
  background-color: #d9ecff !important;
  border-color: #b3d8ff;
}

.collapse-btn i {
  margin-right: 2px;
  font-size: 10px;
}

.collapse-text {
  font-size: 10px;
  font-weight: 500;
}

/* 批量指派功能样式 */
.batch-assignment-items {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  background-color: #fafafa;
}

.batch-summary {
  margin-top: 12px;
  padding: 8px 12px;
  background-color: #e8f4fd;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.summary-text {
  color: #606266;
  font-size: 14px;
}

.summary-text strong {
  color: #409eff;
  font-weight: 600;
}

/* 批量指派按钮样式修复 */
.section-actions .el-button--primary {
  color: #ffffff !important;
}

.section-actions .el-button--primary:disabled {
  color: #a8abb2 !important;
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
}

/* 优化复选框样式 */
.cycle-items-table .el-table__header-wrapper .el-checkbox,
.cycle-items-table .el-table__body-wrapper .el-checkbox {
  transform: scale(1.2);
}

.cycle-items-table .el-table__header-wrapper .el-checkbox__inner,
.cycle-items-table .el-table__body-wrapper .el-checkbox__inner {
  width: 16px;
  height: 16px;
  border: 2px solid #dcdfe6;
  border-radius: 3px;
  background-color: #fff;
  transition: all 0.3s ease;
}

.cycle-items-table .el-table__header-wrapper .el-checkbox__inner:hover,
.cycle-items-table .el-table__body-wrapper .el-checkbox__inner:hover {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.cycle-items-table .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #409eff !important;
  border-color: #409eff !important;
}

.cycle-items-table .el-checkbox__input.is-checked .el-checkbox__inner::after {
  border: 2px solid #fff;
  border-left: 0;
  border-top: 0;
  height: 8px;
  left: 4px;
  top: 1px;
  width: 4px;
}

.cycle-items-table .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #409eff !important;
  border-color: #409eff !important;
}

.cycle-items-table .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
  background-color: #fff;
  height: 2px;
  left: 3px;
  top: 6px;
  width: 8px;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }
  
  .search-form {
    padding: 12px;
  }
  
  .el-form--inline .el-form-item {
    display: block;
    margin-right: 0;
    margin-bottom: 12px;
  }
  
  .el-form-item .el-input,
  .el-form-item .el-select {
    width: 100% !important;
  }
  
  .el-table {
    font-size: 12px;
  }
  
  .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }
  
  .card-subtitle {
    display: none;
  }
  
  .el-table-column {
    min-width: 80px;
  }
}
</style>
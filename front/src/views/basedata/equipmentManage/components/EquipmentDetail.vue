<template>
  <div class="equipment-detail-container">
    <!-- 二维码显示区域 -->
    <div class="qrcode-container">
      <div class="qrcode-wrapper">
        <h4>设备二维码</h4>
        <vue-qrcode
          :value="qrCodeUrl"
          :options="qrCodeOptions"
          class="qrcode"
        />
        <p class="qrcode-tip">扫码查看设备详情</p>
      </div>
    </div>

    <!-- 设备详情内容 -->
    <div class="detail-content">
      <!-- 一、基础信息类 -->
      <el-divider content-position="left">
        <span style="font-weight: bold; color: #409EFF;">一、基础信息类</span>
      </el-divider>
    <el-descriptions :column="2" border>
      <el-descriptions-item label="设备编号">{{ equipmentData.equipmentNumber || '-' }}</el-descriptions-item>
      <el-descriptions-item label="测量设备名称">{{ equipmentData.equipmentName || '-' }}</el-descriptions-item>
      <el-descriptions-item label="启用日期">{{ equipmentData.enableDate || '-' }}</el-descriptions-item>
      <el-descriptions-item label="设备类型">{{ equipmentData.equipmentType || '-' }}</el-descriptions-item>
      <el-descriptions-item label="型号规格">{{ equipmentData.modelSpecification || '-' }}</el-descriptions-item>
      <el-descriptions-item label="出厂编号">{{ equipmentData.factoryNumber || '-' }}</el-descriptions-item>
      <el-descriptions-item label="制造商" :span="2">{{ equipmentData.manufacturer || '-' }}</el-descriptions-item>
    </el-descriptions>

    <!-- 二、技术参数类 -->
    <el-divider content-position="left">
      <span style="font-weight: bold; color: #409EFF;">二、技术参数类</span>
    </el-divider>
    <el-descriptions :column="1" border>
      <el-descriptions-item label="指标特性">
        <div style="white-space: pre-wrap;">{{ equipmentData.indicatorCharacteristics || '-' }}</div>
      </el-descriptions-item>
    </el-descriptions>

    <!-- 三、校准/溯源管理类 -->
    <el-divider content-position="left">
      <span style="font-weight: bold; color: #409EFF;">三、校准/溯源管理类</span>
    </el-divider>
    <el-descriptions :column="2" border>
      <el-descriptions-item label="检定/校准机构">{{ equipmentData.calibrationInstitution || '-' }}</el-descriptions-item>
      <el-descriptions-item label="溯源方式">{{ equipmentData.traceabilityMethod || '-' }}</el-descriptions-item>
      <el-descriptions-item label="本次校准日期">{{ equipmentData.currentCalibrationDate || '-' }}</el-descriptions-item>
      <el-descriptions-item label="证书编号">{{ equipmentData.certificateNumber || '-' }}</el-descriptions-item>
      <el-descriptions-item label="下次校准日期">{{ equipmentData.nextCalibrationDate || '-' }}</el-descriptions-item>
      <el-descriptions-item label="间隔日期">{{ equipmentData.intervalDays ? equipmentData.intervalDays : '-' }}</el-descriptions-item>
      <el-descriptions-item label="期间核查日期" :span="2">{{ equipmentData.interimCheckDate || '-' }}</el-descriptions-item>
    </el-descriptions>
    
    <el-descriptions :column="1" border style="margin-top: 10px;">
      <el-descriptions-item label="备注(校准确认)">
        <div style="white-space: pre-wrap;">{{ equipmentData.calibrationRemark || '-' }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="校准内容">
        <div style="white-space: pre-wrap;">{{ equipmentData.calibrationContent || '-' }}</div>
      </el-descriptions-item>
    </el-descriptions>

    <!-- 四、管理责任类 -->
    <el-divider content-position="left">
      <span style="font-weight: bold; color: #409EFF;">四、管理责任类</span>
    </el-divider>
    <el-descriptions :column="2" border>
      <el-descriptions-item label="管理者">{{ equipmentData.manager || '-' }}</el-descriptions-item>
      <el-descriptions-item label="设备状态">
        <el-tag
          :type="equipmentData.equipmentStatus === '正常' ? 'success' : 
                 equipmentData.equipmentStatus === '维修中' ? 'warning' : 
                 equipmentData.equipmentStatus === '停用' ? 'info' : 'danger'"
        >
          {{ equipmentData.equipmentStatus || '未设置' }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="放置地点" :span="2">{{ equipmentData.location || '-' }}</el-descriptions-item>
    </el-descriptions>

    <!-- 五、财务与合同类 -->
    <el-divider content-position="left">
      <span style="font-weight: bold; color: #409EFF;">五、财务与合同类</span>
    </el-divider>
    <el-descriptions :column="2" border>
      <el-descriptions-item label="金额">{{ equipmentData.amount ? '¥' + equipmentData.amount : '-' }}</el-descriptions-item>
      <el-descriptions-item label="合同">{{ equipmentData.contract || '-' }}</el-descriptions-item>
      <el-descriptions-item label="发票" :span="2">{{ equipmentData.invoice || '-' }}</el-descriptions-item>
    </el-descriptions>

      <!-- 操作按钮 -->
      <div style="text-align: center; margin-top: 20px;">
        <el-button @click="close">关 闭</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import VueQrcode from 'vue-qrcode'

const emit = defineEmits(['close'])

const props = defineProps({
  equipmentData: {
    type: Object,
    default: () => ({})
  }
})

// 生成二维码URL
const qrCodeUrl = computed(() => {
  if (!props.equipmentData.id) return ''

  // 获取当前页面的host和path
  const currentHost = window.location.origin
  const basePath = '/equipment-detail-h5'

  // 构造H5页面URL，包含设备ID参数
  return `${currentHost}${basePath}?id=${props.equipmentData.id}`
})

// 二维码配置选项
const qrCodeOptions = {
  width: 150,
  height: 150,
  margin: 1,
  color: {
    dark: '#000000',
    light: '#FFFFFF'
  },
  errorCorrectionLevel: 'M'
}

function close() {
  emit('close')
}
</script>

<style scoped>
.equipment-detail-container {
  position: relative;
  display: flex;
  gap: 20px;
}

.qrcode-container {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
}

.qrcode-wrapper {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.qrcode-wrapper h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.qrcode {
  display: block;
  margin: 0 auto;
}

.qrcode-tip {
  margin: 10px 0 0 0;
  font-size: 12px;
  color: #909399;
}

.detail-content {
  flex: 1;
  margin-right: 200px; /* 为二维码留出空间 */
}

.el-divider {
  margin: 20px 0;
}

.el-divider__text {
  font-size: 16px;
}

.el-descriptions {
  margin-bottom: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .equipment-detail-container {
    flex-direction: column;
  }

  .qrcode-container {
    position: static;
    order: -1;
    margin-bottom: 20px;
  }

  .detail-content {
    margin-right: 0;
  }
}
</style>

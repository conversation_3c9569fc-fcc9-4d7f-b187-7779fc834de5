<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备编号" prop="equipmentNumber">
        <el-input
          v-model="queryParams.equipmentNumber"
          placeholder="请输入设备编号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备名称" prop="equipmentName">
        <el-input
          v-model="queryParams.equipmentName"
          placeholder="请输入设备名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备类型" prop="equipmentType">
        <el-input
          v-model="queryParams.equipmentType"
          placeholder="请输入设备类型"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备状态" prop="equipmentStatus">
        <el-select
          v-model="queryParams.equipmentStatus"
          placeholder="请选择设备状态"
          clearable
          style="width: 200px"
        >
          <el-option label="正常" value="正常" />
          <el-option label="维修中" value="维修中" />
          <el-option label="停用" value="停用" />
          <el-option label="报废" value="报废" />
        </el-select>
      </el-form-item>
      <el-form-item label="管理者" prop="manager">
        <el-input
          v-model="queryParams.manager"
          placeholder="请输入管理者"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="制造商" prop="manufacturer">
        <el-input
          v-model="queryParams.manufacturer"
          placeholder="请输入制造商"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Upload"
          @click="handleImport"
        >导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExportTemplate"
        >模板</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="equipmentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="设备编号" align="center" prop="equipmentNumber" width="120" />
      <el-table-column label="设备名称" align="center" prop="equipmentName" width="200" show-overflow-tooltip />
      <el-table-column label="设备类型" align="center" prop="equipmentType" width="120" />
      <el-table-column label="型号规格" align="center" prop="modelSpecification" width="150" show-overflow-tooltip />
      <el-table-column label="制造商" align="center" prop="manufacturer" width="150" show-overflow-tooltip />
      <el-table-column label="设备状态" align="center" prop="equipmentStatus" width="100">
        <template #default="scope">
          <el-tag
            :type="scope.row.equipmentStatus === '正常' ? 'success' : 
                   scope.row.equipmentStatus === '维修中' ? 'warning' : 
                   scope.row.equipmentStatus === '停用' ? 'info' : 'danger'"
          >
            {{ scope.row.equipmentStatus || '未设置' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="管理者" align="center" prop="manager" width="100" />
      <el-table-column label="放置地点" align="center" prop="location" width="150" show-overflow-tooltip />
      <el-table-column label="启用日期" align="center" prop="enableDate" width="120" />
      <el-table-column label="下次校准日期" align="center" prop="nextCalibrationDate" width="140" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改设备对话框 -->
    <el-dialog :title="title" v-model="open" width="1200px" append-to-body>
      <EquipmentForm
        v-if="open"
        :equipment-data="form"
        :is-edit="isEdit"
        @submit="handleFormSubmit"
        @cancel="cancel"
      />
    </el-dialog>

    <!-- 查看设备详情对话框 -->
    <el-dialog title="设备详情" v-model="viewOpen" width="1200px" append-to-body>
      <EquipmentDetail
        v-if="viewOpen"
        :equipment-data="viewData"
        @close="viewOpen = false"
      />
    </el-dialog>

    <!-- 导入对话框 -->
    <EquipmentImport
      :visible="importOpen"
      @update:visible="importOpen = $event"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script setup name="EquipmentManagement">
import { 
  listEquipmentManagement, 
  getEquipmentManagement, 
  delEquipmentManagement, 
  batchDelEquipmentManagement,
  downloadEquipmentManagementTemplate
} from "@/api/basedata/equipmentManagement"
import EquipmentForm from './components/EquipmentForm.vue'
import EquipmentDetail from './components/EquipmentDetail.vue'
import EquipmentImport from './components/EquipmentImport.vue'

const { proxy } = getCurrentInstance()

const equipmentList = ref([])
const open = ref(false)
const viewOpen = ref(false)
const importOpen = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const isEdit = ref(false)

const data = reactive({
  form: {},
  viewData: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    equipmentNumber: null,
    equipmentName: null,
    equipmentType: null,
    equipmentStatus: null,
    manager: null,
    manufacturer: null
  }
})

const { queryParams, form, viewData } = toRefs(data)

/** 查询设备列表 */
function getList() {
  loading.value = true
  listEquipmentManagement(queryParams.value).then(response => {
    equipmentList.value = response.data.rows
    total.value = response.data.total
    loading.value = false
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.value = {}
  proxy.resetForm("equipmentRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加设备"
  isEdit.value = false
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const equipmentId = row.id || ids.value[0]
  getEquipmentManagement(equipmentId).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改设备"
    isEdit.value = true
  })
}

/** 查看按钮操作 */
function handleView(row) {
  const equipmentId = row.id
  getEquipmentManagement(equipmentId).then(response => {
    viewData.value = response.data
    viewOpen.value = true
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const equipmentIds = row.id || ids.value
  proxy.$modal.confirm('是否确认删除设备编号为"' + (row.equipmentNumber || equipmentIds) + '"的数据项？').then(function() {
    if (Array.isArray(equipmentIds)) {
      return batchDelEquipmentManagement(equipmentIds)
    } else {
      return delEquipmentManagement(equipmentIds)
    }
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导入按钮操作 */
function handleImport() {
  importOpen.value = true
}

/** 导入成功回调 */
function handleImportSuccess() {
  getList()
}

/** 下载模板操作 */
function handleExportTemplate() {
  downloadEquipmentManagementTemplate().then(response => {
    // 创建blob对象
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '设备管理_导入模板.xlsx'
    document.body.appendChild(link)
    link.click()

    // 清理
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  }).catch(error => {
    console.error('下载模板失败:', error)
    proxy.$modal.msgError("下载模板失败")
  })
}

/** 表单提交 */
function handleFormSubmit() {
  open.value = false
  getList()
}

onMounted(() => {
  getList()
})
</script>

import request from '@/utils/request'

// 查询样品记录列表
export function listSampleRecords(query) {
  return request({
    url: '/sampling/sample-records',
    method: 'get',
    params: query
  })
}



// 查询任务分组的样品记录列表
export function getSampleRecordsByGroup(groupId) {
  return request({
    url: `/sampling/sample-records/group/${groupId}`,
    method: 'get'
  })
}

// 查询样品记录详细
export function getSampleRecord(recordId) {
  return request({
    url: `/sampling/sample-records/${recordId}`,
    method: 'get'
  })
}

// 新增样品记录
export function addSampleRecord(data) {
  return request({
    url: '/sampling/sample-records/create',
    method: 'post',
    data: data
  })
}

// 修改样品记录
export function updateSampleRecord(data) {
  return request({
    url: '/sampling/sample-records',
    method: 'put',
    data: data
  })
}

// 更新样品记录状态
export function updateSampleRecordStatus(recordId, status) {
  return request({
    url: `/sampling/sample-records/status/${recordId}`,
    method: 'put',
    data: status
  })
}

// 删除样品记录
export function delSampleRecord(recordId) {
  return request({
    url: `/sampling/sample-records/${recordId}`,
    method: 'delete'
  })
}



// 为任务分组生成样品记录
export function generateSampleRecordsForGroup(groupId) {
  return request({
    url: `/sampling/sample-records/generate/group/${groupId}`,
    method: 'post'
  })
}



// 批量更新样品记录状态
export function batchUpdateSampleRecordStatus(data) {
  return request({
    url: '/sampling/sample-records/batch-status',
    method: 'put',
    data: data
  })
}
